"""
Configuration settings for DS Task AI News application.
"""
import os
from typing import List, Dict
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # API Keys
    cohere_api_key: str = os.getenv("COHERE_API_KEY", "")
    groq_api_key: str = os.getenv("GROQ_API_KEY", "")
    pinecone_api_key: str = os.getenv("PINECONE_API_KEY", "")

    # Pinecone settings
    pinecone_environment: str = os.getenv("PINECONE_ENVIRONMENT", "")
    pinecone_index_name: str = os.getenv("PINECONE_INDEX_NAME", "news-articles")
    pinecone_namespace: str = os.getenv("PINECONE_NAMESPACE", "default")
    
    # Data directories
    raw_news_dir: str = os.getenv("RAW_NEWS_DIR", "./data/raw_news")
    processed_news_dir: str = os.getenv("PROCESSED_NEWS_DIR", "./data/processed_news")
    
    # RSS Feed URLs organized by category
    rss_feeds: Dict[str, List[str]] = {
        "mainstream_news": [
            "https://feeds.bbci.co.uk/news/rss.xml",
            "https://rss.cnn.com/rss/edition.rss",
            "https://feeds.reuters.com/reuters/topNews",
        ],
        "music": [
            "https://www.billboard.com/feed/",
            "https://www.rollingstone.com/music/rss/",
        ],
        "gaming": [
            "https://feeds.ign.com/ign/games-all",
            "https://www.gamesradar.com/rss/",
        ],
        "tech": [
            "https://www.wired.com/feed/rss",
            "https://www.techradar.com/rss",
        ],
        "lifestyle": [
            "https://lifehacker.com/rss",
            "https://www.vogue.com/feed/rss",
            "https://www.foodnetwork.com/feeds/all/rss.xml",
        ]
    }
    
    # Embedding settings
    embedding_model: str = "embed-english-v3.0"
    embedding_dimension: int = 1024
    
    # LLM settings
    groq_model: str = "mixtral-8x7b-32768"
    max_tokens: int = 1000
    temperature: float = 0.7
    
    # News fetching settings
    fetch_interval_hours: int = 6
    max_articles_per_feed: int = 50
    
    # API settings
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_reload: bool = True
    
    # Recommendation settings
    max_recommendations: int = 10
    similarity_threshold: float = 0.7
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings

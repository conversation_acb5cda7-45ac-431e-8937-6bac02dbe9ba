# DS Task AI News

DS Task AI News is an AI-powered news retrieval system that gathers news articles from various online sources, stores them in a vector database, and enables users to discover relevant articles based on their interests. The system uses advanced AI techniques to find and recommend related news articles dynamically.

## Features

- **News Aggregation**: Fetches news using categorized RSS feeds (Mainstream News, Music, Gaming, Tech, Lifestyle)
- **Vector Database Storage**: Stores news articles in Pinecone for efficient similarity searches
- **AI-powered Recommendations**: Uses Cohere embeddings and re-ranking to provide relevant news recommendations
- **LLM-powered Analysis**: Utilizes Groq for AI-driven insights and trending topic analysis
- **FastAPI Backend**: RESTful API with automatic documentation
- **Real-time Processing**: Background news fetching and processing

## Quick Start

### Prerequisites

- Python 3.8 or higher
- API keys for [Cohere](https://dashboard.cohere.ai/), [Groq](https://console.groq.com/), and [Pinecone](https://app.pinecone.io/)

### Installation

1. **Clone and setup:**

   ```bash
   git clone <repository-url>
   cd ds_task_ai_news
   ./setup.sh  # On Windows: setup.bat
   ```

2. **Configure API keys:**
   Edit `.env` file and add your API keys:

   ```env
   COHERE_API_KEY=your_cohere_api_key_here
   GROQ_API_KEY=your_groq_api_key_here
   PINECONE_API_KEY=your_pinecone_api_key_here
   PINECONE_ENVIRONMENT=your_pinecone_environment_here
   ```

3. **Run the backend:**

   ```bash
   ./run.sh  # On Windows: run.bat
   ```

4. **Start the frontend (in a new terminal):**

   ```bash
   cd frontend
   ./start-frontend.sh  # On macOS/Linux
   # or
   python serve.py      # On Windows
   ```

5. **Access the application:**
   - **Frontend UI**: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## API Endpoints

- `POST /search` - Search for news articles (optionally by category)
- `POST /fetch-news` - Fetch latest news from RSS feeds
- `POST /fetch-news/{category}` - Fetch news from specific category
- `GET /categories` - Get available news categories
- `GET /trending` - Get trending topics
- `GET /stats` - Get database statistics
- `GET /docs` - Interactive API documentation

## Project Structure

```
ds_task_ai_news/
├── backend/
│   ├── main.py              # FastAPI application
│   ├── config.py            # Configuration management
│   ├── news_fetcher.py      # RSS feed fetching
│   ├── vector_store.py      # Pinecone operations
│   ├── embeddings.py        # Cohere embeddings
│   ├── recommender.py       # AI recommendations
│   └── requirements.txt     # Python dependencies
├── frontend/
│   ├── index.html           # Main HTML interface
│   ├── css/styles.css       # Styling
│   ├── js/app.js           # JavaScript functionality
│   ├── serve.py            # Frontend server
│   └── start-frontend.sh   # Frontend startup script
├── data/
│   ├── raw_news/           # Raw news articles
│   └── processed_news/     # Processed articles
├── docs/
│   ├── API.md              # API documentation
│   └── SETUP.md            # Detailed setup guide
├── setup.sh / setup.bat    # Setup scripts
├── run.sh / run.bat        # Run scripts
└── .env.example            # Environment template
```

## Documentation

- [API Documentation](docs/API.md) - Complete API reference
- [Setup Guide](docs/SETUP.md) - Detailed installation and configuration
- [Interactive API Docs](http://localhost:8000/docs) - When running locally

## Technologies Used

**Backend:**

- **FastAPI** - Modern Python web framework
- **Pinecone** - Vector database for similarity search
- **Cohere** - Embeddings and re-ranking
- **Groq** - Fast LLM inference
- **Loguru** - Advanced logging
- **Pydantic** - Data validation

**Frontend:**

- **HTML5** - Modern semantic markup
- **CSS3** - Responsive design with Flexbox/Grid
- **JavaScript (ES6+)** - Interactive functionality
- **Font Awesome** - Icons and visual elements

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For questions or issues, please check the documentation or create an issue in the repository.
